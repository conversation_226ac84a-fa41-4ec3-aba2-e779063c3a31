import { useState } from "react";

interface Option {
  name: string;
  value: string;
  label: string;
  disabled?: boolean;
}

interface Props {
  options: Option[];
  defaultValues?: string[];
  onChange?: (selected: string[]) => void;
}

export const CheckboxGroup = ({ options, defaultValues = [], onChange }: Props) => {
  const [selected, setSelected] = useState<string[]>(defaultValues);

  const handleChange = (value: string, checked: boolean) => {
    const newSelected = checked
      ? [...selected, value]
      : selected.filter((v) => v !== value);
    setSelected(newSelected);
    onChange?.(newSelected);
  };

  return (
    <div style={{ marginTop: "10px" }}>
      {options.map(({ name, value, label, disabled }) => (
        <label key={value} style={{ display: "block", marginBottom: "5px" }}>
          <input
            type="checkbox"
            name={name}
            value={value}
            disabled={disabled}
            checked={selected.includes(value)}
            onChange={(e) => handleChange(value, e.target.checked)}
          />
          {label}
        </label>
      ))}
    </div>
  );
};
