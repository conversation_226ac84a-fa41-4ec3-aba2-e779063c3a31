export const CheckboxController = ({
  name,
  label,
  defaultChecked = false,
  onChange,
}: {
  name: string;
  label: string;
  defaultChecked?: boolean;
  onChange?: (checked: boolean) => void;
}) => {
  return (
    <label>
      <input
        type="checkbox"
        name={name}
        defaultChecked={defaultChecked}
        onChange={(e) => onChange?.(e.target.checked)}
      />
      {label}
    </label>
  );
};
