.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 8px;
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 16px;
  height: 16px;
  border: 2px solid #555;
  margin-right: 8px;
  display: inline-block;
  position: relative;
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '';
  position: absolute;
  width: 10px;
  height: 5px;
  border-left: 2px solid #000;
  border-bottom: 2px solid #000;
  top: 4px;
  left: 3px;
  transform: rotate(-45deg);
}

.checkbox-text {
  font-size: 14px;
}
