import { useState } from "react";
import { CheckboxController, CheckboxGroup } from "./components";

function App() {
  const [count, setCount] = useState(0);

  return (
    <>
      <h1>Custom Checkbox Library</h1>
      <p>Count: {count}</p>
      <button onClick={() => setCount(count + 1)}>Click me</button>

      <CheckboxController
        name="subscribe"
        label="Subscribe to updates"
        onChange={(checked) => console.log("Subscribed:", checked)}
      />

      <CheckboxGroup
        options={[
          { name: "airFilter", value: "airFilter", label: "Air Filter" },
          { name: "oil", value: "oil", label: "Engine Oil" },
          { name: "brake", value: "brake", label: "Brake Pads" },
        ]}
        defaultValues={["oil"]}
        onChange={(selected) => console.log("Selected items:", selected)}
      />
    </>
  );
}

export default App;
